const mongoose = require('mongoose');
const User = require('../models/User');
const College = require('../models/College');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const adminAccounts = [
  {
    name: '<PERSON>ghu Admin',
    email: '<EMAIL>',
    password: 'raghu#1',
    collegeShortName: 'IIIT Delhi'
  }
  // {
  //   name: '<PERSON><PERSON> Admin',
  //   email: '<EMAIL>',
  //   password: 'admin123',
  //   collegeShortName: 'DTU'
  // }
];

async function seedAdmins() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    for (const adminData of adminAccounts) {
      // Find the college
      const college = await College.findOne({ shortName: adminData.collegeShortName });
      if (!college) {
        console.log(`College ${adminData.collegeShortName} not found, skipping admin creation`);
        continue;
      }

      // Delete existing admin if exists
      await User.deleteOne({ email: adminData.email });

      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(adminData.password, salt);

      // Create admin
      const admin = await User.create({
        name: adminData.name,
        email: adminData.email,
        password: hashedPassword,
        role: 'admin',
        college: college._id,
        isActive: true,
        isEmailVerified: true, // Admin accounts are pre-verified
        emailVerificationToken: null,
        emailVerificationExpires: null
      });

      console.log('Created admin:', {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        college: admin.college,
        role: admin.role
      });
    }

  } catch (error) {
    console.error('Error creating admins:', error);
  } finally {
    await mongoose.connection.close();
    console.log('Database connection closed');
  }
}

// Run the seeding function
seedAdmins(); 