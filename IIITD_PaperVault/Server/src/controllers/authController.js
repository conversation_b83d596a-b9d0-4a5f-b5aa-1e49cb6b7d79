const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const crypto = require('crypto'); // Add this for token generation
const User = require('../models/User');
const { sendEmail } = require('../utils/email'); // Import email utility


// **forgot Password Controller**
exports.forgotPassword = async (req, res) => {
  const { email } = req.body;  
  console.log("email", email);

  try {
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    const resetToken = jwt.sign({ email }, process.env.JWT_SECRET, { expiresIn: "1h" });

    // Store token directly in the database
    user.resetToken = resetToken;
    user.resetTokenExpiry = Date.now() + 3600000; // 1 hour expiry
    await user.save();

    // Frontend reset link
    const resetLink = `${process.env.FRONTEND_URL}/reset-password?token=${resetToken}`;

    // Attempt to send email
    if (process.env.EMAIL_HOST) {
      try {
        await sendEmail({
        to: user.email,
        subject: "Password Reset Request",
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: auto; padding: 20px; background-color: #f9f9f9; border: 1px solid #e0e0e0; border-radius: 8px;">
            <h2 style="color: #333;">Password Reset Request</h2>
      
            <p style="font-size: 16px; color: #555;">
              We received a request to reset your password. If this was you, you can reset your password by clicking the button below:
            </p>
      
            <div style="text-align: center; margin: 30px 0;">
              <a href="${resetLink}" style="background-color: #007BFF; color: white; padding: 12px 20px; text-decoration: none; border-radius: 5px; font-size: 16px;">
                Reset Password
              </a>
            </div>
      
            <p style="font-size: 16px; color: #555;">
              If you didn't request this, you can safely ignore this email—no changes will be made to your account.
            </p>
      
            <hr style="border: none; border-top: 1px solid #ddd; margin: 30px 0;" />
      
            <p style="font-size: 16px; color: #555;">Best regards,</p>
            <p style="font-size: 16px; font-weight: bold; color: #333;">CampusPYQ Security Team</p>
          </div>
        `
      });
      

        res.json({ message: "Password reset email sent." });
      } catch (emailError) {
        console.error("Error sending email:", emailError);
        res.status(500).json({ message: "User found, but email could not be sent.", error: emailError.message });
      }
    } else {
      // Email not configured - return error message
      res.status(500).json({
        message: "Email service not configured. Please contact administrator or use alternative reset method."
      });
    }

  } catch (error) {
    logger.error("Error processing forgot password request:", error);
    res.status(500).json({ message: "Internal server error", error: error.message });
  }
};

// **Reset Password Controller**

exports.resetPassword = async (req, res) => {
  const { token, newPassword } = req.body;


  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    const user = await User.findOne({ email: decoded.email });

    if (!user) {
      return res.status(404).json({ message: "Invalid token or user not found" });
    }

    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(newPassword, salt);
    user.password = hashedPassword;
    user.resetToken = null;
    await user.save();

    res.json({ message: "Password reset successfully" });
  } catch (error) {
    res.status(400).json({ message: "Invalid or expired token" });
  }
};



// Register a new user
exports.register = async (req, res) => {
  try {
    const { name, email, password } = req.body;

    // Validate required fields
    if (!name || !email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide all required fields'
      });
    }

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({
        success: false,
        message: 'User already exists'
      });
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const tokenExpiration = new Date();
    tokenExpiration.setHours(tokenExpiration.getHours() + 24); // Token valid for 24 hours

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create user with verification token
    const user = new User({
      name,
      email,
      password: hashedPassword,
      emailVerificationToken: verificationToken,
      emailVerificationExpires: tokenExpiration
    });

    // Save user
    await user.save();

    // Create verification URL
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${verificationToken}`;

    // Send verification email if email is configured
    if (process.env.EMAIL_HOST && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
      // Send verification email when email service is properly configured
      try {
        await sendEmail({
          to: email,
          subject: 'Verify Your Email Address – Campus Past Papers',
      html: `
       <div style="font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f4f6f9; padding: 30px;">
  <div style="max-width: 600px; margin: auto; background-color: #ffffff; border-radius: 10px; overflow: hidden; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
    
    <!-- Header Banner -->
    <div style="background-color: #3498db; padding: 20px 30px;">
      <h1 style="margin: 0; color: #fff; font-size: 24px; text-align: center;">Welcome to Campus Past Papers, ${name}!</h1>
    </div>

    <!-- Body Content -->
    <div style="padding: 30px;">
      <p style="font-size: 16px; color: #34495e; line-height: 1.6;">Thank you for joining our community! We're excited to have you with us. To get started, please verify your email address by clicking the button below:</p>
      
      <!-- Verify Email Button -->
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationUrl}" 
           style="background-color: #3498db; color: white; padding: 14px 28px; text-decoration: none; font-weight: bold; border-radius: 6px; display: inline-block; font-size: 16px;">
          Verify My Email
        </a>
      </div>

      <!-- Expiry Notice -->
      <p style="font-size: 14px; color: #7f8c8d; text-align: center;">
        This verification link will expire in 24 hours. If you didn’t create this account, you can safely ignore this email.
      </p>

      <!-- Divider -->
      <hr style="border: none; border-top: 1px solid #ecf0f1; margin: 30px 0;">

      <!-- Closing Text -->
      <p style="font-size: 14px; color: #95a5a6; text-align: center;">Thanks,<br><strong>The Campus Past Papers Team</strong></p>
    </div>
  </div>
</div>

      `
        });
      } catch (emailError) {
        console.error('Email sending failed:', emailError);
        // Continue with registration even if email fails
      }
    } else {
      // Email service not configured, automatically verify the user
      user.isEmailVerified = true;
      user.emailVerificationToken = undefined;
      user.emailVerificationExpires = undefined;
      await user.save();
      console.log('Email service not configured: Email verification skipped for user:', email);
    }

    // Return success message (don't send token yet since email is not verified)
    res.status(201).json({
      success: true,
      message: 'Registration successful! Please check your email to verify your account.'
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during registration',
      error: error.message
    });
  }
};

// Login user - update this function
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    // Validate required fields
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        message: 'Please provide email and password'
      });
    }

    // Find user and populate college information
    const user = await User.findOne({ email }).populate('college', 'name code');
    if (!user) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }
    
    // Check if email is verified (skip for admin accounts)
    if (!user.isEmailVerified && user.role !== 'admin' && user.role !== 'superadmin') {

      return res.status(401).json({
        success: false,
        message: 'Please verify your email before logging in',
        isEmailVerified: false
      });
    }

    // Check if user is reported
    if (user.isReported) {
      let reportDate = user.reportedAt ? new Date(user.reportedAt).toLocaleDateString() : 'an unspecified date';
      return res.status(403).json({
        success: false,
        message: `Your account has been reported by an administrator. Access to this platform has been restricted.`,
        isReported: true,
        reportReason: user.reportReason || 'Violation of platform terms and conditions',
        reportedAt: reportDate
      });
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      return res.status(401).json({
        success: false,
        message: 'Invalid email or password'
      });
    }

    // Create JWT token
    const token = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );

    // Return user data (excluding password) and token
    const userResponse = {
      _id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      avatar: user.avatar,
      college: user.college
    };

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: userResponse
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during login',
      error: error.message
    });
  }
};

// Add this new controller function
exports.resendVerificationEmail = async (req, res) => {
  try {
    const { email } = req.body;
    
    if (!email) {
      return res.status(400).json({
        success: false,
        message: 'Please provide an email address'
      });
    }
    
    // Find user by email
    const user = await User.findOne({ email });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    if (user.isEmailVerified) {
      return res.status(400).json({
        success: false,
        message: 'Email is already verified'
      });
    }
    
    // Generate new verification token
    const verificationToken = crypto.randomBytes(32).toString('hex');
    const tokenExpiration = new Date();
    tokenExpiration.setHours(tokenExpiration.getHours() + 24);
    
    // Update user with new token
    user.emailVerificationToken = verificationToken;
    user.emailVerificationExpires = tokenExpiration;
    await user.save();
    
    // Create verification URL
    const verificationUrl = `${req.protocol}://${req.get('host')}/api/auth/verify-email/${verificationToken}`;
    
    // Send verification email
    await sendEmail({
      to: email,
      subject: 'Please verify your email address',
      html: `
        <h1>Email Verification</h1>
        <p>Hello ${user.name},</p>
        <p>Please verify your email address by clicking the link below:</p>
        <a href="${verificationUrl}">Verify Email</a>
        <p>This link will expire in 24 hours.</p>
        <p>If you did not request this email, please ignore it.</p>
      `
    });
    
    res.json({
      success: true,
      message: 'Verification email resent successfully'
    });
  } catch (error) {
    console.error('Resend verification email error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error',
      error: error.message
    });
  }
};
exports.verifyEmail = async (req, res) => {
  try {
    const { token } = req.params;
    
    // Find user with this token and token not expired
    const user = await User.findOne({
      emailVerificationToken: token,
      emailVerificationExpires: { $gt: Date.now() }
    });
    
    if (!user) {
      return res.status(400).json({
        success: false,
        message: 'Invalid or expired verification token'
      });
    }
    
    // Update user as verified
    user.isEmailVerified = true;
    user.emailVerificationToken = undefined;
    user.emailVerificationExpires = undefined;
    await user.save();
    
    // Create JWT token for auto-login after verification
    const jwtToken = jwt.sign(
      { userId: user._id, role: user.role },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    // Redirect to frontend with token
    res.redirect(`${process.env.FRONTEND_URL}/email-verified?token=${jwtToken}`);
  } catch (error) {
    console.error('Email verification error:', error);
    res.status(500).json({
      success: false,
      message: 'Server error during email verification',
      error: error.message
    });
  }
};

// Google OAuth callback handler
exports.googleAuthCallback = async (req, res) => {
  try {
    if (!req.user) {
      return res.redirect(`${process.env.FRONTEND_URL}/login?error=auth_failed`);
    }

    // Generate JWT token
    const token = jwt.sign(
      { userId: req.user._id, role: req.user.role },
      process.env.JWT_SECRET,
      { expiresIn: '24h' }
    );
    
    // Redirect to frontend with token
    res.redirect(`${process.env.FRONTEND_URL}/oauth-callback?token=${token}`);
  } catch (error) {
    console.error('Google auth callback error:', error);
    res.redirect(`${process.env.FRONTEND_URL}/login?error=server_error`);
  }
};