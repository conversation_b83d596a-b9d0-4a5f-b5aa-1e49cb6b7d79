const nodemailer = require('nodemailer');
const config = require('../config/config');

const transporter = nodemailer.createTransport({
  host: config.EMAIL_HOST,
  port: config.EMAIL_PORT,
  secure: config.EMAIL_PORT === 465,
  auth: {
    user: config.EMAIL_USER,
    pass: config.EMAIL_PASS,
  },
});

exports.sendEmail = async ({ to, subject, text, html }) => {
  try {
    console.log('Attempting to send email to:', to);
    console.log('Email configuration check:');
    console.log('- HOST:', config.EMAIL_HOST);
    console.log('- PORT:', config.EMAIL_PORT);
    console.log('- USER:', config.EMAIL_USER);
    console.log('- FROM:', config.EMAIL_FROM);
    console.log('- PASS configured:', !!config.EMAIL_PASS);

    const mailOptions = {
      from: config.EMAIL_FROM,
      to: Array.isArray(to) ? to.join(',') : to,
      subject,
      text,
      html,
    };

    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully!');
    console.log('Message ID:', info.messageId);
    console.log('Response:', info.response);
    return info;
  } catch (error) {
    console.error('❌ Email sending failed:');
    console.error('Error code:', error.code);
    console.error('Error message:', error.message);

    if (error.code === 'EAUTH') {
      console.error('Authentication failed - check Gmail App Password');
    } else if (error.code === 'ECONNECTION') {
      console.error('Connection failed - check network/firewall');
    } else if (error.code === 'EMESSAGE') {
      console.error('Message rejected - check email content');
    }

    throw new Error(`Failed to send email: ${error.message}`);
  }
};