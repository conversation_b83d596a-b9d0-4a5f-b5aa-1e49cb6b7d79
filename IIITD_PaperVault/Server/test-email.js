require('dotenv').config();
const nodemailer = require('nodemailer');

// Test email configuration
const testEmail = async () => {
  console.log('Testing email configuration...');
  console.log('EMAIL_HOST:', process.env.EMAIL_HOST);
  console.log('EMAIL_PORT:', process.env.EMAIL_PORT);
  console.log('EMAIL_USER:', process.env.EMAIL_USER);
  console.log('EMAIL_PASS:', process.env.EMAIL_PASS ? '***configured***' : 'NOT SET');
  
  const transporter = nodemailer.createTransporter({
    host: process.env.EMAIL_HOST,
    port: process.env.EMAIL_PORT,
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_USER,
      pass: process.env.EMAIL_PASS,
    },
  });

  try {
    // Verify connection configuration
    await transporter.verify();
    console.log('✅ SMTP connection verified successfully!');
    
    // Send test email
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: '<EMAIL>', // Replace with your test email
      subject: 'Test Email from Campus PYQ',
      html: `
        <h2>Email Configuration Test</h2>
        <p>This is a test email to verify that the email configuration is working correctly.</p>
        <p>Sent from: ${process.env.EMAIL_FROM}</p>
        <p>Time: ${new Date().toISOString()}</p>
      `,
    });
    
    console.log('✅ Test email sent successfully!');
    console.log('Message ID:', info.messageId);
    
  } catch (error) {
    console.error('❌ Email configuration error:', error.message);
    
    if (error.code === 'EAUTH') {
      console.error('Authentication failed. Please check:');
      console.error('1. Gmail App Password is correct');
      console.error('2. 2-Factor Authentication is enabled');
      console.error('3. App Password was generated correctly');
    }
  }
};

testEmail();
