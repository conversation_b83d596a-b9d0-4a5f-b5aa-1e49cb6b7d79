require('dotenv').config();
const cloudinary = require('./src/config/cloudinary');

const testCloudinary = async () => {
  console.log('Testing Cloudinary configuration...');
  console.log('CLOUD_NAME:', process.env.CLOUD_NAME);
  console.log('CLOUD_API_KEY:', process.env.CLOUD_API_KEY);
  console.log('CLOUD_API_SECRET:', process.env.CLOUD_API_SECRET ? '***configured***' : 'NOT SET');

  try {
    // Test the connection by getting account details
    const result = await cloudinary.api.ping();
    console.log('✅ Cloudinary connection successful!');
    console.log('Response:', result);
    
    // Get account usage info
    const usage = await cloudinary.api.usage();
    console.log('📊 Account Usage:');
    console.log('- Plan:', usage.plan);
    console.log('- Credits used:', usage.credits.used);
    console.log('- Credits limit:', usage.credits.limit);
    
  } catch (error) {
    console.error('❌ Cloudinary connection failed:');
    console.error('Error:', error.message);
    
    if (error.http_code === 401) {
      console.error('Authentication failed. Please check:');
      console.error('1. Cloud name is correct');
      console.error('2. API key is correct');
      console.error('3. API secret is correct');
    }
  }
};

testCloudinary();
